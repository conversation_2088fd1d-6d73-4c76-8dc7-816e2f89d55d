# Changelog

## Future

- Remove consent dialog again
- Drag and drop to reorder search engines

## v4.4.2

- Add privacy consent dialog to pass Mozilla's review

## v4.4.1

- Add privacy policy
- Add version info

## v4.4.0

- Support POST requests
- Update translations

## v4.3.1

- Add old Google image search as an option (might not work for everyone)

## v4.3.0

- Add advanced options per search engine:
  - Do not encode image URL
  - Strip protocol from image URL
- Fix dependent checkboxes not reacting to changes

## v4.2.1

- Fix browser check

## 4.2.0

- Port to TypeScript
- Make it work in Firefox again, including custom icons
- Various bugfixes

## 4.1.2

- Replace Google Image Search with Google Lens because Google removed the old reverse search. This will also apply to
  current users.

## 4.1.1

- Add Content-Security-Policy for options page
- Select new search engine by default

## 4.1.0

- Add option to show 'Open All' as the first item

## 4.0.0

- Complete rewrite with Manifest v3 and Svelte framework with a slightly updated UI
- Search engines can be reordered
- Option to hide "Open all"
- Option to not create a submenu, but always search in all engines
- Dropped custom icons, will be re-integrated when Firefox supports Manifest v3

## 3.4.4

- Fix saving options

## 3.4.3

- Fix checkboxes

## 3.4.2

- Update to Bootstrap v4.0.0 Stable to comply with Mozilla's AMO rules

## 3.4.1

- Replace Google Image Search with Google Lens because Google removed the old reverse search. This will also apply to
  current users.

## 3.4.0

- Add an option to open all providers by default (thanks Delgan)

## 3.3.5

- Support tab relationship for Tree Style Tab
- Text fixes

## 3.3.4

- Swedish, French, Portugese, Japanese and Spanish translation - thanks to bjrnbrg, xerta555, rafaelndev, ScratchBuild
  and tmc81

## 3.3.3

- Added Turkish language
- Bugfixes

## 3.3.2

- adress Mozilla's complaints

## 3.3.1

- Updated Simplifed Chinese

## 3.3.0

- If multiple search engines are selected, a new entry "Open All" will be shown, which opens all search engines.

## 3.2.0

- Custom icon
- Updated German translation
- Updated README

## 3.1.4

- Critical bug fixes

## 3.1.3

- Fix context menu not working

## 3.1.2

- Fix empty storage check

## 3.1.1

- Fix TinEye search

## 3.1.0

- Multiple search engines can be added and named

## 3.0.2

- Add simplified Chinese

## 3.0.1

- Icons in context menu (only Firefox >= 56)

## 3.0.0

- More than one search engine can be selected now
- Bugfixes

## 2.1.2

- Correct capitalization of the addon name
- Add SauceNAO and IQDB

## 2.1.1

- Bugfix if CSE has no %s in URL
- Updated Chrome port

## 2.1.0

- possible to add custom search engine

## 2.0.0

- Complete rewrite (Firefox-only for now)
- Support more search engines
- Vastly improved settings page
- Tab behaviour can be changed (open left/right to the current tab or at the end)
- Localize everything (needed to drop polish language, sorry!)
- Settings should now be synced with the Firefox profile

## 1.2.0

- Open image search page in the tab next to the current tab

## 1.1.0

- added option to open results page in new tab

## 1.0.2

- added polish language - thanks to wmq21 for the translation

## 1.0.1

- Extension added to Opera Store
- Removed redundant "if"
- Added comments and gitignore

## 1.0.0

- Initial release
