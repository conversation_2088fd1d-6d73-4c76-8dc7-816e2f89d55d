{"addSearchProvider": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "contextMenuOpenAll": {"message": "<PERSON><PERSON><PERSON> todos"}, "contextMenuTitle": {"message": "Pesquisa Reversa de Imagens"}, "errorWhileSaving": {"message": "Houve um erro desconhecido ao salvar."}, "extensionDescription": {"message": "Adicionar uma opção ao menu de contexto para fazer uma busca reversa de uma imagem em vários serviços"}, "extensionName": {"message": "Pesquisa Reversa de Imagens"}, "msgAtLeastOneSearchProvider": {"message": "Por favor escolha pelo menos um provedor de busca!"}, "msgDuplicatedProviderName": {"message": "Algum provedor de busca está com o nome duplicado!"}, "msgIconUploadNotImage": {"message": "Por favor envie um formato de imagem válido! (png, jpg,ico)"}, "msgIconUploadNotSquareImage": {"message": "Por favor envie uma imagem quadrada!"}, "msgIconUploadNotSupported": {"message": "O uso de ícones em menus contextuais não é suportado por este navegador."}, "msgSuccessSaveOptions": {"message": "Salvo!"}, "openInBackgroundLabel": {"message": "Abrir em Segundo Plano"}, "openTabAtEnd": {"message": "No fim"}, "openTabAtLabel": {"message": "Abrir a página de busca"}, "openTabAtLeft": {"message": "A esquerda da guia atual"}, "openTabAtRight": {"message": "A direita da guia atual"}, "optionsPageTitle": {"message": "Opções"}, "providerNamePlaceholder": {"message": "2-15 letras"}, "providerNamePlaceholderError": {"message": "O nome do provedor $index$ é inválido. Por favor use de 2-15 letras.", "placeholders": {"index": {"content": "$1"}}}, "providerURLPlaceholder": {"message": "http(s):// no início e %s para a url da imagem..."}, "providerURLPlaceholderPOST": {"message": "http(s):// no início..."}, "providerURLPlaceholderError": {"message": "A url do provedor $index$ é invalido. Use http(s):// no inicio e %s como espaço reservado para a url da imagem.", "placeholders": {"index": {"content": "$1"}}}, "providerURLPlaceholderErrorPOST": {"message": "A URL do provedor $index$ é inválida. Use http(s):// no início.", "placeholders": {"index": {"content": "$1"}}}, "doNotEncodeUrlLabel": {"message": "Não codificar URL"}, "stripProtocolLabel": {"message": "Remover protocolo da URL da imagem"}, "httpMethodLabel": {"message": "Método HTTP"}, "postFieldNameLabel": {"message": "Nome do campo POST"}, "contentTypeLabel": {"message": "Tipo de Conteúdo"}, "restoreDefaultSearchProviders": {"message": "<PERSON><PERSON><PERSON>"}, "saveOptions": {"message": "<PERSON><PERSON>"}, "searchAllByDefault": {"message": "Pesquisar em todos os provedores por padrão"}, "searchAllByDefault_info": {"message": "Quando esta opção está habilitada, apenas uma entrada do menu contextual é mostrada e clicar nela abrirá todos os provedores de pesquisa selecionados. Se estiver desabilitada, um submenu será criado."}, "showOpenAll": {"message": "<PERSON>rar '<PERSON><PERSON><PERSON>'"}, "showOpenAllAtTop": {"message": "Mostrar '<PERSON><PERSON><PERSON>' no topo"}, "redirectingToSearchEngine": {"message": "Redirecionando para o mecanismo de busca..."}, "privacyPolicy": {"message": "Política de Privacidade"}, "version": {"message": "Versão $version$", "placeholders": {"version": {"content": "$1"}}}, "dragToReorder": {"message": "Arrastar para reordenar"}}