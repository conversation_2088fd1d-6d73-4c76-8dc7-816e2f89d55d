function base64ToBlob(base64Data, mimeType = 'image/jpeg') {
  const byteCharacters = atob(base64Data);
  const byteNumbers = new Array(byteCharacters.length);
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }
  const byteArray = new Uint8Array(byteNumbers);
  return new Blob([byteArray], { type: mimeType });
}

function submitPostForm(action, fieldName, fieldValue, contentType, isFileData = false) {
  const form = document.createElement('form');
  form.method = 'POST';
  form.action = action;

  if (contentType && contentType !== 'application/x-www-form-urlencoded') {
    form.enctype = contentType;
  }

  if (isFileData && contentType === 'multipart/form-data') {
    // For file data with multipart/form-data, create a file input
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.name = fieldName;
    fileInput.style.display = 'none';

    // Convert base64 to blob and create a file
    try {
      const blob = base64ToBlob(fieldValue);
      const file = new File([blob], 'image.jpg', { type: 'image/jpeg' });

      // Create a DataTransfer object to set the file
      const dataTransfer = new DataTransfer();
      dataTransfer.items.add(file);
      fileInput.files = dataTransfer.files;

      form.appendChild(fileInput);
    } catch (error) {
      console.error('Failed to create file from base64 data:', error);
      // Fallback to hidden input with base64 data
      const input = document.createElement('input');
      input.type = 'hidden';
      input.name = fieldName;
      input.value = fieldValue;
      form.appendChild(input);
    }
  } else {
    // For URL data or file data with application/x-www-form-urlencoded
    const input = document.createElement('input');
    input.type = 'hidden';
    input.name = fieldName;
    input.value = fieldValue;
    form.appendChild(input);
  }

  document.body.appendChild(form);
  form.submit();
}

window.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SUBMIT_POST_FORM') {
    const { action, fieldName, fieldValue, contentType, isFileData } = event.data;
    submitPostForm(action, fieldName, fieldValue, contentType, isFileData);
  }
});

document.addEventListener('DOMContentLoaded', () => {
  const loadingText = document.getElementById('loading-text');
  if (loadingText && chrome.i18n) {
    const translatedText = chrome.i18n.getMessage('redirectingToSearchEngine');
    if (translatedText) {
      loadingText.textContent = translatedText;
    }
  }

  const urlParams = new URLSearchParams(window.location.search);
  if (
    urlParams.has('action') &&
    urlParams.has('fieldName') &&
    urlParams.has('fieldValue')
  ) {
    const action = decodeURIComponent(urlParams.get('action'));
    const fieldName = decodeURIComponent(urlParams.get('fieldName'));
    const fieldValue = decodeURIComponent(urlParams.get('fieldValue'));
    const contentType = urlParams.get('contentType')
      ? decodeURIComponent(urlParams.get('contentType'))
      : 'application/x-www-form-urlencoded';
    const isFileData = urlParams.get('isFileData') === 'true';

    submitPostForm(action, fieldName, fieldValue, contentType, isFileData);
  }
});
