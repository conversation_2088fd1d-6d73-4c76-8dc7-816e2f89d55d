@import "bootstrap/scss/functions";

$enable-gradients: true;

@import "bootstrap/scss/variables";
@import "bootstrap/scss/maps";
@import "bootstrap/scss/mixins";
@import "bootstrap/scss/root";

@import "bootstrap/scss/utilities";
@import "bootstrap/scss/utilities/api";
@import "bootstrap/scss/reboot";
@import "bootstrap/scss/type";

@import "bootstrap/scss/containers";
@import "bootstrap/scss/forms";
@import "bootstrap/scss/grid";

@import "bootstrap/scss/alert";
@import "bootstrap/scss/buttons";
@import "bootstrap/scss/badge";
@import "bootstrap/scss/close";
@import "bootstrap/scss/tooltip";

.pointer {
  cursor: pointer;
}

.drag-handle {
  cursor: grab;
  color: $secondary;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.375rem 0.75rem;
  user-select: none;

  &:hover {
    background-color: $gray-200;
  }

  &:active {
    cursor: grabbing;
  }
}

.provider-item {
  transition: transform 0.2s ease;

  &.dragging {
    opacity: 0.5;
  }
}

.drop-zone {
  position: relative;

  &.drag-over::before {
    content: '';
    position: absolute;
    top: -2px;
    left: 0;
    right: 0;
    height: 4px;
    background-color: $primary;
    border-radius: 2px;
    z-index: 10;
  }
}
